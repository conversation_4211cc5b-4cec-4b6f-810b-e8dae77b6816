use super::service::AccountService;
use crate::models::account::{Account, AccountConfig, AccountCookie};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::State;

/// 添加账号请求
#[derive(Debug, Deserialize)]
pub struct AddAccountRequest {
    pub name: String,
    pub description: Option<String>,
    pub cookie: Option<String>,
}

/// 通过登录添加账号请求
#[derive(Debug, Deserialize)]
pub struct AddAccountViaLoginRequest {
    pub name: String,
    pub description: Option<String>,
}

/// 更新账号请求
#[derive(Debug, Deserialize)]
pub struct UpdateAccountRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub enabled: Option<bool>,
    pub priority: Option<u32>,
}

/// 更新Cookie请求
#[derive(Debug, Deserialize)]
pub struct UpdateCookieRequest {
    pub cookie_value: String,
}

/// 账号响应信息
#[derive(Debug, Serialize)]
pub struct AccountResponse {
    pub account: Account,
    pub message: String,
    pub is_update: bool, // 是否为更新操作
}

/// 获取所有账号
#[tauri::command]
pub async fn account_get_all(
    account_service: State<'_, AccountService>,
) -> Result<Vec<Account>, String> {
    Ok(account_service.get_all_accounts().await)
}

/// 获取指定账号
#[tauri::command]
pub async fn account_get(
    account_service: State<'_, AccountService>,
    accountId: String,
) -> Result<Account, String> {
    account_service
        .get_account(&accountId)
        .await
        .ok_or_else(|| format!("账号不存在: {}", accountId))
}

/// 添加账号
#[tauri::command]
pub async fn account_add(
    account_service: State<'_, AccountService>,
    request: AddAccountRequest,
) -> Result<AccountResponse, String> {
    println!("🎯 account_add 命令开始执行: {:?}", request);

    let account = account_service
        .add_account(request.name, request.description, request.cookie)
        .await?;

    let response = AccountResponse {
        account,
        message: "账号添加成功".to_string(),
        is_update: false,
    };

    println!("✅ account_add 命令执行成功，返回响应");
    Ok(response)
}

/// 通过登录添加账号
#[tauri::command]
pub async fn account_add_via_login(
    account_service: State<'_, AccountService>,
    request: AddAccountViaLoginRequest,
) -> Result<AccountResponse, String> {
    println!("🎯 account_add_via_login 命令开始执行: {:?}", request);

    let account = account_service
        .add_account_via_login(request.name, request.description)
        .await?;

    let response = AccountResponse {
        account,
        message: "账号通过登录添加成功".to_string(),
        is_update: false,
    };

    println!("✅ account_add_via_login 命令执行成功，返回响应");
    Ok(response)
}

/// 删除账号
#[tauri::command]
pub async fn account_remove(
    account_service: State<'_, AccountService>,
    accountId: String,
) -> Result<String, String> {
    account_service.remove_account(&accountId).await?;
    Ok(format!("账号 {} 已删除", accountId))
}

/// 更新账号信息
#[tauri::command]
pub async fn account_update(
    account_service: State<'_, AccountService>,
    accountId: String,
    request: UpdateAccountRequest,
) -> Result<String, String> {
    account_service
        .update_account(
            &accountId,
            request.name,
            request.description,
            request.enabled,
            request.priority,
        )
        .await?;
    Ok(format!("账号 {} 已更新", accountId))
}

/// 更新账号Cookie
#[tauri::command]
pub async fn account_update_cookie(
    account_service: State<'_, AccountService>,
    accountId: String,
    cookieValue: String,
) -> Result<String, String> {
    account_service
        .update_account_cookie(&accountId, cookieValue)
        .await?;
    Ok(format!("账号 {} Cookie已更新", accountId))
}

/// 获取用户信息（验证Cookie）
#[tauri::command]
pub async fn account_get_user_info(
    account_service: State<'_, AccountService>,
    cookie: String,
) -> Result<crate::services::account::service::UserInfo, String> {
    account_service.get_user_info(&cookie).await
}

/// 使用用户信息添加账号请求
#[derive(Debug, Deserialize)]
pub struct AddAccountWithUserInfoRequest {
    pub nickname: String,
    pub user_id: Option<String>,
    pub cookie: String,
    pub description: Option<String>,
}

/// 使用用户信息添加账号
#[tauri::command]
pub async fn account_add_with_user_info(
    account_service: State<'_, AccountService>,
    nickname: String,
    userId: Option<String>,
    cookie: String,
    description: Option<String>,
) -> Result<AccountResponse, String> {
    println!(
        "🎯 account_add_with_user_info 命令开始执行: nickname={}, user_id={:?}",
        nickname, userId
    );

    let (account, is_update) = account_service
        .add_account_with_user_info(nickname, userId, cookie, description)
        .await?;

    let message = if is_update {
        "账号已存在，信息已更新".to_string()
    } else {
        "账号添加成功".to_string()
    };

    let response = AccountResponse {
        account,
        message,
        is_update,
    };

    println!("✅ account_add_with_user_info 命令执行成功，返回响应");
    Ok(response)
}

/// 自动更新账号Cookie（通过登录窗口）
#[tauri::command]
pub async fn account_auto_update_cookie(
    goldfish_business: State<'_, crate::business::GoldfishBusiness>,
    account_id: String,
) -> Result<(), String> {
    println!(
        "🎯 account_auto_update_cookie 命令开始执行: account_id={}",
        account_id
    );

    goldfish_business.update_account_cookie(&account_id).await?;

    println!("✅ account_auto_update_cookie 命令执行成功");
    Ok(())
}

/// 验证账号Cookie
#[tauri::command]
pub async fn account_validate_cookie(
    account_service: State<'_, AccountService>,
    accountId: String,
) -> Result<bool, String> {
    account_service.validate_account_cookie(&accountId).await
}

/// 验证所有启用账号的Cookie
#[tauri::command]
pub async fn account_validate_all(
    account_service: State<'_, AccountService>,
) -> Result<String, String> {
    account_service.validate_all_accounts().await?;
    Ok("所有启用账号Cookie验证通过".to_string())
}

/// 获取下一个可用账号
#[tauri::command]
pub async fn account_get_next_available(
    account_service: State<'_, AccountService>,
) -> Result<Account, String> {
    let selection = account_service.get_next_available_account().await?;
    Ok(selection.account)
}

/// 记录账号使用结果
#[tauri::command]
pub async fn account_record_usage(
    account_service: State<'_, AccountService>,
    accountId: String,
    success: bool,
    errorMessage: Option<String>,
) -> Result<String, String> {
    account_service
        .record_account_usage(&accountId, success, errorMessage)
        .await?;
    Ok(format!("账号 {} 使用记录已更新", accountId))
}

/// 获取账号配置
#[tauri::command]
pub async fn account_get_config(
    account_service: State<'_, AccountService>,
) -> Result<AccountConfig, String> {
    Ok(account_service.get_config().await)
}

/// 获取账号统计信息
#[tauri::command]
pub async fn account_get_stats(
    account_service: State<'_, AccountService>,
) -> Result<HashMap<String, serde_json::Value>, String> {
    Ok(account_service.get_account_stats().await)
}

/// 清理失效账号
#[tauri::command]
pub async fn account_cleanup_failed(
    account_service: State<'_, AccountService>,
    max_failures: u32,
) -> Result<Vec<String>, String> {
    account_service.cleanup_failed_accounts(max_failures).await
}

/// 批量验证所有账号Cookie
#[tauri::command]
pub async fn account_validate_all_cookies(
    account_service: State<'_, AccountService>,
) -> Result<HashMap<String, bool>, String> {
    let accounts = account_service.get_all_accounts().await;
    let mut results = HashMap::new();

    for account in accounts {
        if account.cookie.is_some() {
            match account_service.validate_account_cookie(&account.id).await {
                Ok(is_valid) => {
                    results.insert(account.id, is_valid);
                }
                Err(_) => {
                    results.insert(account.id, false);
                }
            }
        } else {
            results.insert(account.id, false);
        }
    }

    Ok(results)
}

/// 启用/禁用账号
#[tauri::command]
pub async fn account_set_enabled(
    account_service: State<'_, AccountService>,
    accountId: String,
    enabled: bool,
) -> Result<String, String> {
    account_service
        .update_account(&accountId, None, None, Some(enabled), None)
        .await?;
    let status = if enabled { "启用" } else { "禁用" };
    Ok(format!("账号 {} 已{}", accountId, status))
}

/// 设置账号优先级
#[tauri::command]
pub async fn account_set_priority(
    account_service: State<'_, AccountService>,
    accountId: String,
    priority: u32,
) -> Result<String, String> {
    account_service
        .update_account(&accountId, None, None, None, Some(priority))
        .await?;
    Ok(format!("账号 {} 优先级已设置为 {}", accountId, priority))
}

/// 获取可用账号数量
#[tauri::command]
pub async fn account_get_available_count(
    account_service: State<'_, AccountService>,
) -> Result<usize, String> {
    let config = account_service.get_config().await;
    Ok(config.available_count())
}

/// 获取启用账号数量
#[tauri::command]
pub async fn account_get_enabled_count(
    account_service: State<'_, AccountService>,
) -> Result<usize, String> {
    let config = account_service.get_config().await;
    Ok(config.enabled_count())
}

/// 重置账号统计信息
#[tauri::command]
pub async fn account_reset_stats(
    account_service: State<'_, AccountService>,
    accountId: String,
) -> Result<String, String> {
    // 这个功能需要在AccountService中添加对应方法
    Err("功能暂未实现，需要在AccountService中添加reset_stats方法".to_string())
}

/// 导出账号配置
#[tauri::command]
pub async fn account_export_config(
    account_service: State<'_, AccountService>,
) -> Result<String, String> {
    let config = account_service.get_config().await;
    serde_json::to_string_pretty(&config).map_err(|e| format!("导出配置失败: {}", e))
}

/// 处理账号验证请求（打开验证窗口）
#[tauri::command]
pub async fn account_handle_verification(
    goldfish_business: State<'_, crate::business::GoldfishBusiness>,
    accountId: String,
) -> Result<(), String> {
    println!("🔐 处理账号验证请求: account_id={}", accountId);

    goldfish_business
        .handle_account_verification(&accountId)
        .await?;

    println!("✅ 账号验证窗口已打开");
    Ok(())
}

/// 完成账号验证（验证窗口关闭后调用）
#[tauri::command]
pub async fn account_verification_completed(
    goldfish_business: State<'_, crate::business::GoldfishBusiness>,
    accountId: String,
) -> Result<(), String> {
    println!("✅ 账号验证完成: account_id={}", accountId);

    goldfish_business
        .handle_account_verification_completed(&accountId)
        .await?;

    println!("✅ 账号验证完成处理成功");
    Ok(())
}

/// 导入账号配置
#[tauri::command]
pub async fn account_import_config(
    account_service: State<'_, AccountService>,
    configJson: String,
) -> Result<String, String> {
    let config: AccountConfig =
        serde_json::from_str(&configJson).map_err(|e| format!("解析配置失败: {}", e))?;

    // 验证配置
    config.validate()?;

    // 保存配置
    account_service.save_config(&config).await?;

    Ok(format!("成功导入 {} 个账号", config.accounts.len()))
}

/// 测试账号连接
#[tauri::command]
pub async fn account_test_connection(
    account_service: State<'_, AccountService>,
    accountId: String,
) -> Result<bool, String> {
    // 这里应该实际测试账号的API连接
    // 暂时只验证Cookie
    account_service.validate_account_cookie(&accountId).await
}

/// 获取账号轮询状态
#[tauri::command]
pub async fn account_get_rotation_status(
    account_service: State<'_, AccountService>,
) -> Result<serde_json::Value, String> {
    let config = account_service.get_config().await;
    let stats = account_service.get_account_stats().await;

    let mut status = serde_json::Map::new();
    status.insert(
        "rotation_strategy".to_string(),
        serde_json::Value::String(format!("{:?}", config.rotation_config.strategy)),
    );
    status.insert(
        "min_interval_per_account".to_string(),
        serde_json::Value::Number(config.rotation_config.min_interval_per_account.into()),
    );
    status.insert(
        "max_consecutive_failures".to_string(),
        serde_json::Value::Number(config.rotation_config.max_consecutive_failures.into()),
    );
    status.insert(
        "failure_cooldown_seconds".to_string(),
        serde_json::Value::Number(config.rotation_config.failure_cooldown_seconds.into()),
    );

    // 合并统计信息
    for (key, value) in stats {
        status.insert(key, value);
    }

    Ok(serde_json::Value::Object(status))
}
